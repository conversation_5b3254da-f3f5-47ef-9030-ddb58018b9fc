import { AxonCard } from 'ui/UIComponents';
import MarkdownToJsx from '../Chatbox/MarkdownToJsx';
import { BotMessageToSend } from '../types';
import ChatAvatar from './ChatAvatar';
import { ChatLoadingIcon } from './ChatLoadingIcon';
import MessageWrapper from './MessageWrapper';
type Props = { message: { id: string } & BotMessageToSend };

const BotMessage = ({ message }: Props) => {
  return (
    <MessageWrapper>
      <ChatAvatar />
      <AxonCard className='bot-message p-2'>
        {message.type === 'loading' && <ChatLoadingIcon />}
        <MarkdownToJsx content={message.content} />
      </AxonCard>
    </MessageWrapper>
  );
};

export default BotMessage;
