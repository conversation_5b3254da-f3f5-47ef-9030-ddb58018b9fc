import { memo } from 'react';
import { useGetMessageById } from '../chatbotStore';
import BotMessage from './BotMessage';
import UserMessage from './UserMessage';

const ChatMessage = memo(({ messageId }: { messageId: string }) => {
  const message = useGetMessageById(messageId);

  if (!message) return null;

  const owner = message.owner;
  if (owner === 'user') return <UserMessage content={message.content} />;
  return <BotMessage message={message} />;
});

ChatMessage.displayName = 'ChatMessage';

export default ChatMessage;
