import { AxonCard } from 'ui/UIComponents';
import ChatAvatar from './ChatAvatar';
import MessageWrapper from './MessageWrapper';

type Props = {
  content: string;
};

const UserMessage = ({ content }: Props) => {
  return (
    <MessageWrapper isUserMess>
      <AxonCard className='user-message p-2'>{content}</AxonCard>
      <ChatAvatar isUserMess name='MV' />
    </MessageWrapper>
  );
};

export default UserMessage;
