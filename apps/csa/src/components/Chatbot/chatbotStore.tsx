import { enableMapSet } from 'immer';
import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useShallow } from 'zustand/react/shallow';
import { ChatMessageEntity, ChatMessageToSend, createSelectors } from './types';
// Enable Map/Set support in Immer
enableMapSet();
type ChatbotStore = {
  messages: Map<string, ChatMessageEntity>;
  messageHasStarted: boolean;
  isWaiting: boolean;
  chatbotUrl: string;
  sendMessage: (message: ChatMessageToSend) => void;
  updateMessage: (params: { id: string; content: string }) => void;
  startChatMessage: () => void;
  initChatbotUrl: () => void;
  sendLoadingMessage: () => void;
  removeLoadingMessage: () => void;
};

export const useChatbotStoreBase = create<ChatbotStore>()(
  persist(
    immer(
      devtools(
        (set, get) => ({
          messages: new Map<string, ChatMessageEntity>(),
          messageHasStarted: false,
          isWaiting: false,
          chatbotUrl: '',
          initChatbotUrl: () => {
            set((state) => {
              // Only init once
              if (state.chatbotUrl) return;

              const clientId = uuidv4();
              const baseUrl = process.env.CHATBOT_WS_URL ?? `ws://vedge-staging.axon-networks.com/neura-agent/agent`;
              state.chatbotUrl = `${baseUrl}/${clientId}`;
            });
          },
          startChatMessage: () => {
            set((state) => {
              if (state.messageHasStarted) return;
              state.messageHasStarted = true;
            });
          },
          sendMessage: (message: ChatMessageToSend) => {
            set((state) => {
              const newMessage = {
                ...message,
                id: message.id || uuidv4(),
                createdAt: message['createdAt'] || new Date().toISOString(),
              };
              state.messages.set(newMessage.id, newMessage);
            });
          },
          updateMessage: ({ id, content }) => {
            set((state) => {
              const message = state.messages.get(id);
              if (message) {
                message.content = content;
              }
            });
          },
          sendLoadingMessage: () => {
            get().sendMessage({ owner: 'bot', content: '', type: 'loading' });
            set((state) => {
              state.isWaiting = true;
            });
          },
          removeLoadingMessage: () => {
            set((state) => {
              // Get the last message ID from the Map (Maps preserve insertion order)
              const messageIds = Array.from(state.messages.keys());
              const lastMessageId = messageIds[messageIds.length - 1];
              if (lastMessageId) {
                state.messages.delete(lastMessageId);
              }
              state.isWaiting = false;
            });
          },
        }),
        // Define instance and name for devtools
        { name: 'chatbotStore', store: 'chatbotStore' },
      ),
    ),
    {
      name: 'chatbotStore',
      storage: createJSONStorage(() => localStorage, {
        reviver: (key, value) => {
          if (key === 'messages' && Array.isArray(value)) {
            // Convert array of [key, value] pairs back to Map
            return new Map(value);
          }
          return value;
        },
        replacer: (key, value) => {
          if (key === 'messages' && value instanceof Map) {
            // Convert Map to array of [key, value] pairs for serialization
            return Array.from(value.entries());
          }
          return value;
        },
      }),
      partialize: (state) => ({ messages: state.messages }),
    },
  ),
);

export const useChatbotStore = createSelectors(useChatbotStoreBase);
export const useGetMessageById = (id: string) => useChatbotStoreBase((state) => state.messages.get(id));
export const useGetMessageIds = () => useChatbotStoreBase(useShallow((state) => Array.from(state.messages.keys())));
