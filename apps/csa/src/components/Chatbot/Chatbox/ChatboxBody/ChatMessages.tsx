import { useEffect, useRef } from 'react';
import ChatMessage from '../../ChatMessage';
import ChatCurrentDate from './ChatCurrentDate';

const ChatMessages = ({ messageIds }: { messageIds: string[] }) => {
  const endOfMessagesRef = useRef<HTMLDivElement | null>(null);
  const chatBoxRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Scroll to the bottom at first mount
    if (chatBoxRef.current) {
      chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight;
    }
  }, []);

  useEffect(() => {
    // Scroll to the bottom when new messages are added
    if (endOfMessagesRef.current) {
      endOfMessagesRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messageIds]);

  return (
    <div
      className='scrollbar-sm flex flex-col gap-4 overflow-y-auto [&_.bot-message]:rounded-[12px_12px_12px_4px] [&_.user-message]:rounded-[12px_12px_4px_12px]'
      ref={chatBoxRef}>
      <ChatCurrentDate />
      {messageIds.map((messageId) => (
        <ChatMessage key={messageId} messageId={messageId} />
      ))}
      <div ref={endOfMessagesRef} />
    </div>
  );
};

export default ChatMessages;
