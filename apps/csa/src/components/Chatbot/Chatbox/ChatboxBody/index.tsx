import { useGetMessageIds } from '../../chatbotStore';
import ChatboxPlaceholder from './ChatboxPlaceholder';
import ChatMessages from './ChatMessages';
import InitialRequests from './InitialRequests';

const ChatboxBody = () => {
  const messageIds = useGetMessageIds();

  if (messageIds.length > 0) return <ChatMessages messageIds={messageIds} />;
  return (
    <div className='flex flex-col items-center justify-center gap-4'>
      <ChatboxPlaceholder />
      <InitialRequests />
    </div>
  );
};

export default ChatboxBody;
